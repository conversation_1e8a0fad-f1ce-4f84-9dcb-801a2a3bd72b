-- Migration to add pickup_deadline column to parcel_acceptances table
-- This field will store the deadline for pickup (15 minutes after acceptance, extendable by 10 minutes)

ALTER TABLE parcel_acceptances 
ADD COLUMN pickup_deadline DATETIME NULL 
COMMENT 'Deadline for pickup (15 minutes after acceptance, extendable by 10 minutes)';

-- Update existing records to set pickup_deadline to 15 minutes after accepted_at
-- This is for backward compatibility with existing accepted parcels
UPDATE parcel_acceptances 
SET pickup_deadline = DATE_ADD(accepted_at, INTERVAL 15 MINUTE) 
WHERE status = 'accepted' AND pickup_deadline IS NULL;
