const jwt = require("jsonwebtoken");
const Constants = require("../config/Constants");

verifyToken = (req, res, next) => {
  let token = req.headers["x-access-token"];

  if (!token) {
    return res.status(403).send({
      message: "No token provided!"
    });
  }

  jwt.verify(token, Constants.jwt_token_secret, (err, decoded) => {
    if (err) {
      return res.status(401).send({
        message: "Unauthorized!"
      });
    }
    req.accountId = !!decoded.id_account ? decoded.id_account : 0
    req.userId = decoded.id;
    req.token = token
    next();
  });
};

verifyAdminToken = (req, res, next) => {
  let token = req.headers["x-access-token"];

  if (!token) {
    return res.status(403).send({
      message: "No token provided!"
    });
  }

  jwt.verify(token, Constants.jwt_token_admin_secret, (err, decoded) => {
    if (err) {
      return res.status(401).send({
        message: "Unauthorized!"
      });
    }
    req.accountId = !!decoded.id_account ? decoded.id_account : 0
    req.userId = decoded.id;
    req.token = token
    next();
  });
};




const authJwt = {
  verifyToken: verifyToken,
  verifyAdminToken: verifyAdminToken
};
module.exports = authJwt;
