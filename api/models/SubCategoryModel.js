"use strict";

module.exports = function (sequelize, DataTypes) {
  const SubCategoryModel = sequelize.define(
    "SubCategoryModel",
    {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      categoryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "categories",
          key: "id",
        },
      },
      status: {
        type: DataTypes.ENUM("active", "inactive"),
        allowNull: false,
        defaultValue: "active",
      },
      createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "subcategories",
    }
  );

  // Define association
  SubCategoryModel.associate = (models) => {
    SubCategoryModel.belongsTo(models.Category, {
      foreignKey: "categoryId",
      onDelete: "CASCADE",
    });
  };

  return SubCategoryModel;
};
