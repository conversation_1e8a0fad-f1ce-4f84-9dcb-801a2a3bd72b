"use strict";

module.exports = function (sequelize, DataTypes) {
  const ParcelModel = sequelize.define(
    "ParcelModel",
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users", // Name of the Users table
          key: "id",
        },
      },
      categoryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "categories", // Reference to categories table
          key: "id",
        },
      },
      full_name: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Full name of the receiver",
      },
      mobile: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Contact number of the receiver",
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Contact number of the receiver",
      },
      amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: "Price user is willing to pay for delivery",
      },
      weight: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Weight of parcel in kg.",
      },
      insured: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Is the parcel insured?",
      },
      pickup_lat: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: false,
      },
      pickup_long: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: false,
      },
      pickup_address: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      delivery_lat: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: false,
      },
      delivery_long: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: false,
      },
      delivery_address: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      package_type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Additional parcel details",
      },
      length: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Length of the parcel",
      },
      width: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Width of the parcel",
      },
      height: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Height of the parcel",
      },
      title: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Title of the parcel",
      },
      status: {
        type: DataTypes.ENUM("pending", "accepted", "picked", "delivered", "cancelled"),
        allowNull: false,
        defaultValue: "pending",
      },
      createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "parcels",
    }
  );

  ParcelModel.associate = function (models) {
    ParcelModel.belongsTo(models.UserModel, {
      foreignKey: "userId",
      as: "user",
    });

    ParcelModel.belongsTo(models.CategoryModel, {
      foreignKey: "categoryId",
      as: "category",
    });

    ParcelModel.hasMany(models.ParcelMediaModel, {
      foreignKey: "parcelId",
      as: "media",
    });

    ParcelModel.hasOne(models.ParcelAcceptanceModel, {
      foreignKey: "parcel_id",
      as: "acceptance",
    });
  };

  return ParcelModel;
};
