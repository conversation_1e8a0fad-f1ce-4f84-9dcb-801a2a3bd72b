module.exports = (sequelize, DataTypes) => {
  const WalletModel = sequelize.define(
    "WalletModel",
    {
      balance: {
        type: DataTypes.DECIMAL(10, 2),
        defaultValue: 0.0,
        allowNull: false,
      },
    },
    {
      tableName: "wallet",
    }
  );

  WalletModel.associate = (models) => {
    WalletModel.belongsTo(models.User, {
      foreignKey: "user_id",
      as: "user",
      onDelete: "CASCADE",
    });

    WalletModel.hasMany(models.WalletTransaction, {
      foreignKey: "wallet_id",
      as: "transactions",
    });
  };

  return WalletModel;
};
