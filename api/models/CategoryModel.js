"use strict";

module.exports = function (sequelize, DataTypes) {
  const CategoryModel = sequelize.define(
    "CategoryModel",
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM("active", "inactive"),
        allowNull: false,
        defaultValue: "active",
      },
      createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "categories",
    }
  );


  CategoryModel.associate = function (models) {
    CategoryModel.hasMany(models.ParcelModel, {
      foreignKey: "categoryId",
      as: "parcels",
    });
  };

  return CategoryModel;
};
