module.exports = (sequelize, DataTypes) => {
  const WalletTransactionModel = sequelize.define(
    "WalletTransactionModel",
    {
      amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM("credit", "debit"),
        allowNull: false,
      },
      description: {
        type: DataTypes.STRING,
      },
    },
    {
      tableName: "wallet_transaction",
    }
  );

  WalletTransactionModel.associate = (models) => {
    WalletTransactionModel.belongsTo(models.Wallet, {
      foreignKey: "wallet_id",
      as: "wallet",
    });
  };

  return WalletTransactionModel;
};
