'use strict';

module.exports = function (sequelize, DataTypes) {
    const ParcelMediaModel = sequelize.define('ParcelMediaModel', {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true
        },
        parcelId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'parcels', // Name of the Parcels table
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        media_hash: {
            type: DataTypes.STRING,
            allowNull: false,
            comment: "Unique hash of the parcel image"
        },
        media_ext: {
            type: DataTypes.STRING,
            allowNull: false,
            comment: "File extension of the parcel image"
        },
        createdAt: {
            field: 'created_at',
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW
        },
        updatedAt: {
            field: 'updated_at',
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW
        }
    }, {
        tableName: 'parcel_media'
    });

    ParcelMediaModel.associate = function (models) {
        ParcelMediaModel.belongsTo(models.ParcelModel, {
            foreignKey: 'parcelId',
            as: 'parcel'
        });
    };

    return ParcelMediaModel;
};
