"use strict";

module.exports = function (sequelize, DataTypes) {
  const ParcelAcceptanceModel = sequelize.define(
    "ParcelAcceptanceModel",
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      parcel_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "parcels",
          key: "id",
        },
      },
      delivery_user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
      },
      status: {
        type: DataTypes.ENUM("accepted", "picked", "delivered", "cancelled"),
        allowNull: false,
        defaultValue: "accepted",
      },
      otp: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "OTP for verification at pickup",
      },
      accepted_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      picked_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      delivered_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "parcel_acceptances",
    }
  );

  ParcelAcceptanceModel.associate = function (models) {
    ParcelAcceptanceModel.belongsTo(models.ParcelModel, {
      foreignKey: "parcel_id",
      as: "parcel",
    });

    ParcelAcceptanceModel.belongsTo(models.UserModel, {
      foreignKey: "delivery_user_id",
      as: "delivery_user",
    });
  };

  return ParcelAcceptanceModel;
};
