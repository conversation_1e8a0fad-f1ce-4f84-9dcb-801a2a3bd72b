const dbConfig = require("../config/db.config.js");
const Sequelize = require("sequelize");

const sequelize = new Sequelize(dbConfig.DB, dbConfig.USER, dbConfig.PASSWORD, {
  host: dbConfig.HOST,
  dialect: dbConfig.dialect,
  logging: false,
  pool: {
    max: dbConfig.pool.max,
    min: dbConfig.pool.min,
    acquire: dbConfig.pool.acquire,
    idle: dbConfig.pool.idle,
  },
});

async function connectionDb(sequelize) {
  try {
    await sequelize.authenticate();
    console.log("Connection has been established successfully.");
  } catch (error) {
    if (error.code === "PROTOCOL_CONNECTION_LOST") {
      console.error("Unable to connect to the database:", error);
      connectionDb(sequelize);
    } else {
      throw error;
    }
  }
}

connectionDb(sequelize);

const db = {};

db.Sequelize = Sequelize;
db.sequelize = sequelize;

// Models
db.user = require("./UserModel.js")(sequelize, Sequelize);
db.profile = require("./ProfileModel.js")(sequelize, Sequelize);
db.category = require("./CategoryModel.js")(sequelize, Sequelize);
db.subCategory = require("./SubCategoryModel.js")(sequelize, Sequelize);
db.parcel = require("./ParcelModel.js")(sequelize, Sequelize);
db.parcelMedia = require("./ParcelMediaModel.js")(sequelize, Sequelize);
db.parcelAcceptance = require("./ParcelAcceptanceModel.js")(sequelize, Sequelize);
db.wallet = require("./WalletModel.js")(sequelize, Sequelize);
db.walletTransaction = require("./WalletTransactionModel.js")(sequelize, Sequelize);

// Associations
db.profile.belongsTo(db.user, { as: "user", foreignKey: "userId" });
db.user.hasOne(db.profile, { as: "profile", foreignKey: "userId" });

db.category.hasMany(db.subCategory, { as: "subcategories", foreignKey: "categoryId", onDelete: "CASCADE" });
db.subCategory.belongsTo(db.category, { as: "category", foreignKey: "categoryId" });

// Parcel Associations
db.parcel.belongsTo(db.user, { as: "user", foreignKey: "userId", onDelete: "CASCADE" });
db.user.hasMany(db.parcel, { as: "parcels", foreignKey: "userId", onDelete: "CASCADE" });

// Parcel Media Associations
db.parcel.hasMany(db.parcelMedia, { as: "media", foreignKey: "parcelId", onDelete: "CASCADE" });
db.parcelMedia.belongsTo(db.parcel, { as: "parcel", foreignKey: "parcelId", onDelete: "CASCADE" });

// Associate Parcel with Category
db.parcel.belongsTo(db.category, { as: "category", foreignKey: "categoryId", onDelete: "CASCADE" });
db.category.hasMany(db.parcel, { as: "parcels", foreignKey: "categoryId", onDelete: "CASCADE" });

// Parcel Acceptance Associations
db.parcelAcceptance.belongsTo(db.parcel, { as: "parcel", foreignKey: "parcel_id", onDelete: "CASCADE" });
db.parcel.hasOne(db.parcelAcceptance, { as: "acceptance", foreignKey: "parcel_id", onDelete: "CASCADE" });

db.parcelAcceptance.belongsTo(db.user, { as: "deliveryUser", foreignKey: "delivery_user_id", onDelete: "CASCADE" });
db.user.hasMany(db.parcelAcceptance, { as: "acceptedParcels", foreignKey: "delivery_user_id", onDelete: "CASCADE" });

// Wallet Associations
db.wallet.belongsTo(db.user, { as: "user", foreignKey: "userId", onDelete: "CASCADE" });
db.user.hasOne(db.wallet, { as: "wallet", foreignKey: "userId", onDelete: "CASCADE" });

db.walletTransaction.belongsTo(db.wallet, { as: "wallet", foreignKey: "walletId", onDelete: "CASCADE" });
db.wallet.hasMany(db.walletTransaction, { as: "transactions", foreignKey: "walletId", onDelete: "CASCADE" });

module.exports = db;
