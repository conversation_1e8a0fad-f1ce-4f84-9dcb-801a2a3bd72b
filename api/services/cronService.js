const cron = require('cron');
const db = require("../models");
const ParcelModel = db.parcel;
const ParcelAcceptanceModel = db.parcelAcceptance;
const sequelize = db.sequelize;
const { Op } = require("sequelize");

/**
 * Cron job to check for parcels that have exceeded their pickup deadline
 * and return them to the marketplace
 */
const checkPickupTimeouts = new cron.CronJob(
  '*/2 * * * *', // Run every 2 minutes
  async function() {
    try {
      console.log('Running pickup timeout check at:', new Date().toISOString());
      
      const currentTime = new Date();
      
      // Check if pickup_deadline column exists before querying
      let expiredAcceptances = [];
      try {
        // Find all accepted parcels that have exceeded their pickup deadline
        expiredAcceptances = await ParcelAcceptanceModel.findAll({
          where: {
            status: 'accepted',
            pickup_deadline: {
              [Op.lt]: currentTime // pickup_deadline is less than current time
            }
          },
          include: [
            {
              model: ParcelModel,
              as: 'parcel',
              where: {
                status: 'accepted'
              }
            }
          ]
        });
      } catch (dbError) {
        if (dbError.message.includes('pickup_deadline')) {
          console.log('⚠️  pickup_deadline column not found. Please run the database migration first.');
          console.log('📋 Run this SQL: ALTER TABLE parcel_acceptances ADD COLUMN pickup_deadline DATETIME NULL;');
          return;
        }
        throw dbError;
      }

      if (expiredAcceptances.length > 0) {
        console.log(`Found ${expiredAcceptances.length} expired parcel acceptances`);
        
        // Start transaction to ensure data consistency
        const transaction = await sequelize.transaction();
        
        try {
          for (const acceptance of expiredAcceptances) {
            // Delete the acceptance record
            await acceptance.destroy({ transaction });
            
            // Update parcel status back to pending
            await acceptance.parcel.update({ status: "pending" }, { transaction });
            
            console.log(`Returned parcel ${acceptance.parcel_id} to marketplace due to pickup timeout`);
          }
          
          await transaction.commit();
          console.log(`Successfully returned ${expiredAcceptances.length} parcels to marketplace`);
          
        } catch (error) {
          await transaction.rollback();
          console.error('Error in pickup timeout transaction:', error);
        }
      } else {
        console.log('No expired parcel acceptances found');
      }
      
    } catch (error) {
      console.error('Error in pickup timeout check:', error);
    }
  },
  null, // onComplete
  false, // start immediately
  'Asia/Kolkata' // timezone
);

/**
 * Start all cron jobs
 */
const startCronJobs = () => {
  console.log('Starting cron jobs...');
  checkPickupTimeouts.start();
  console.log('Pickup timeout cron job started - runs every 2 minutes');
};

/**
 * Stop all cron jobs
 */
const stopCronJobs = () => {
  console.log('Stopping cron jobs...');
  checkPickupTimeouts.stop();
  console.log('All cron jobs stopped');
};

module.exports = {
  startCronJobs,
  stopCronJobs,
  checkPickupTimeouts
};
