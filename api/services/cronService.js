const db = require("../models");
const ParcelModel = db.parcel;
const ParcelAcceptanceModel = db.parcelAcceptance;
const sequelize = db.sequelize;

// Store active timeouts for each parcel
const activeTimeouts = new Map();

/**
 * Schedule immediate timeout for a specific parcel
 * @param {number} parcelId - The parcel ID
 * @param {Date} pickupDeadline - When the parcel should timeout
 */
const schedulePickupTimeout = (parcelId, pickupDeadline) => {
  // Clear any existing timeout for this parcel
  if (activeTimeouts.has(parcelId)) {
    clearTimeout(activeTimeouts.get(parcelId));
  }

  const currentTime = new Date();
  const timeUntilDeadline = pickupDeadline.getTime() - currentTime.getTime();

  // Only schedule if deadline is in the future
  if (timeUntilDeadline > 0) {
    console.log(`⏰ Scheduling pickup timeout for parcel ${parcelId} in ${Math.round(timeUntilDeadline / 1000)} seconds`);

    const timeoutId = setTimeout(async () => {
      try {
        await returnParcelToMarketplace(parcelId);
        activeTimeouts.delete(parcelId);
      } catch (error) {
        console.error(`Error returning parcel ${parcelId} to marketplace:`, error);
      }
    }, timeUntilDeadline);

    activeTimeouts.set(parcelId, timeoutId);
  } else {
    // Deadline has already passed, return immediately
    console.log(`⚠️ Parcel ${parcelId} deadline has already passed, returning to marketplace immediately`);
    returnParcelToMarketplace(parcelId);
  }
};

/**
 * Cancel scheduled timeout for a parcel (when picked up or cancelled)
 * @param {number} parcelId - The parcel ID
 */
const cancelPickupTimeout = (parcelId) => {
  if (activeTimeouts.has(parcelId)) {
    clearTimeout(activeTimeouts.get(parcelId));
    activeTimeouts.delete(parcelId);
    console.log(`✅ Cancelled pickup timeout for parcel ${parcelId}`);
  }
};

/**
 * Return a parcel to marketplace due to pickup timeout
 * @param {number} parcelId - The parcel ID
 */
const returnParcelToMarketplace = async (parcelId) => {
  try {
    console.log(`🔄 Returning parcel ${parcelId} to marketplace due to pickup timeout`);

    // Find the acceptance record
    const acceptance = await ParcelAcceptanceModel.findOne({
      where: {
        parcel_id: parcelId,
        status: 'accepted'
      },
      include: [
        {
          model: ParcelModel,
          as: 'parcel'
        }
      ]
    });

    if (!acceptance) {
      console.log(`⚠️ No active acceptance found for parcel ${parcelId}`);
      return;
    }

    // Start transaction to ensure data consistency
    const transaction = await sequelize.transaction();

    try {
      // Delete the acceptance record
      await acceptance.destroy({ transaction });

      // Update parcel status back to pending
      await acceptance.parcel.update({ status: "pending" }, { transaction });

      await transaction.commit();
      console.log(`✅ Successfully returned parcel ${parcelId} to marketplace`);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error(`❌ Error returning parcel ${parcelId} to marketplace:`, error);
  }
};

/**
 * Initialize timeout system - schedule timeouts for existing accepted parcels
 */
const initializeTimeoutSystem = async () => {
  try {
    console.log('🚀 Initializing pickup timeout system...');

    // Find all currently accepted parcels and schedule their timeouts
    const acceptedParcels = await ParcelAcceptanceModel.findAll({
      where: {
        status: 'accepted'
      },
      include: [
        {
          model: ParcelModel,
          as: 'parcel',
          where: {
            status: 'accepted'
          }
        }
      ]
    });

    for (const acceptance of acceptedParcels) {
      if (acceptance.pickup_deadline) {
        schedulePickupTimeout(acceptance.parcel_id, new Date(acceptance.pickup_deadline));
      }
    }

    console.log(`✅ Scheduled timeouts for ${acceptedParcels.length} existing accepted parcels`);

  } catch (error) {
    console.error('❌ Error initializing timeout system:', error);
  }
};

/**
 * Start the timeout system
 */
const startTimeoutSystem = () => {
  console.log('🚀 Starting immediate pickup timeout system...');
  initializeTimeoutSystem();
};

/**
 * Stop the timeout system
 */
const stopTimeoutSystem = () => {
  console.log('🛑 Stopping pickup timeout system...');
  // Clear all active timeouts
  for (const [parcelId, timeoutId] of activeTimeouts) {
    clearTimeout(timeoutId);
    console.log(`Cancelled timeout for parcel ${parcelId}`);
  }
  activeTimeouts.clear();
  console.log('All timeouts cleared');
};

module.exports = {
  schedulePickupTimeout,
  cancelPickupTimeout,
  returnParcelToMarketplace,
  startTimeoutSystem,
  stopTimeoutSystem,
  // Legacy exports for backward compatibility
  startCronJobs: startTimeoutSystem,
  stopCronJobs: stopTimeoutSystem
};
