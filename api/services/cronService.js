const db = require("../models");
const ParcelModel = db.parcel;
const ParcelAcceptanceModel = db.parcelAcceptance;
const sequelize = db.sequelize;

// Store active timeouts for each parcel with metadata
const activeTimeouts = new Map();

/**
 * Schedule immediate timeout for a specific parcel
 * @param {number} parcelId - The parcel ID
 * @param {Date} pickupDeadline - When the parcel should timeout
 */
const schedulePickupTimeout = (parcelId, pickupDeadline) => {
  // Generate unique timeout ID for this scheduling
  const uniqueTimeoutId = `${parcelId}_${Date.now()}_${Math.random()}`;

  // FORCE clear any existing timeout for this parcel first
  if (activeTimeouts.has(parcelId)) {
    const oldTimeoutData = activeTimeouts.get(parcelId);
    clearTimeout(oldTimeoutData.timeoutId);
    activeTimeouts.delete(parcelId);
    console.log(`🔄 FORCE cleared existing timeout for parcel ${parcelId} (old ID: ${oldTimeoutData.uniqueId})`);
  }

  const currentTime = new Date();
  const timeUntilDeadline = pickupDeadline.getTime() - currentTime.getTime();

  console.log(`⏰ Parcel ${parcelId}: Current time: ${currentTime.toISOString()}, Deadline: ${pickupDeadline.toISOString()}, Time until deadline: ${Math.round(timeUntilDeadline / 1000)} seconds`);

  // Only schedule if deadline is in the future
  if (timeUntilDeadline > 0) {
    console.log(`✅ Scheduling NEW pickup timeout for parcel ${parcelId} in ${Math.round(timeUntilDeadline / 1000)} seconds (unique ID: ${uniqueTimeoutId})`);

    const timeoutId = setTimeout(async () => {
      try {
        console.log(`⏰ Timeout triggered for parcel ${parcelId} at ${new Date().toISOString()} (unique ID: ${uniqueTimeoutId})`);

        // Double check this timeout is still the active one before proceeding
        if (activeTimeouts.has(parcelId)) {
          const currentTimeoutData = activeTimeouts.get(parcelId);
          if (currentTimeoutData.uniqueId === uniqueTimeoutId) {
            console.log(`✅ Confirmed this is the active timeout for parcel ${parcelId}, proceeding with marketplace return`);
            await returnParcelToMarketplace(parcelId);
            activeTimeouts.delete(parcelId);
          } else {
            console.log(`⚠️ This timeout for parcel ${parcelId} is outdated (current: ${currentTimeoutData.uniqueId}, triggered: ${uniqueTimeoutId}), skipping`);
          }
        } else {
          console.log(`⚠️ No active timeout found for parcel ${parcelId}, was probably cancelled`);
        }
      } catch (error) {
        console.error(`❌ Error in timeout for parcel ${parcelId}:`, error);
        activeTimeouts.delete(parcelId);
      }
    }, timeUntilDeadline);

    // Store timeout with metadata
    activeTimeouts.set(parcelId, {
      timeoutId: timeoutId,
      uniqueId: uniqueTimeoutId,
      deadline: pickupDeadline,
      createdAt: currentTime
    });

    console.log(`📝 Active timeouts count: ${activeTimeouts.size}`);
  } else {
    // Deadline has already passed, return immediately
    console.log(`⚠️ Parcel ${parcelId} deadline has already passed, returning to marketplace immediately`);
    returnParcelToMarketplace(parcelId);
  }
};

/**
 * Cancel scheduled timeout for a parcel (when picked up or cancelled)
 * @param {number} parcelId - The parcel ID
 */
const cancelPickupTimeout = (parcelId) => {
  if (activeTimeouts.has(parcelId)) {
    const timeoutData = activeTimeouts.get(parcelId);
    clearTimeout(timeoutData.timeoutId);
    activeTimeouts.delete(parcelId);
    console.log(`✅ Cancelled pickup timeout for parcel ${parcelId} (unique ID: ${timeoutData.uniqueId}). Active timeouts count: ${activeTimeouts.size}`);
  } else {
    console.log(`⚠️ No active timeout found for parcel ${parcelId} to cancel`);
  }
};

/**
 * Return a parcel to marketplace due to pickup timeout
 * @param {number} parcelId - The parcel ID
 */
const returnParcelToMarketplace = async (parcelId) => {
  try {
    console.log(`🔄 Attempting to return parcel ${parcelId} to marketplace due to pickup timeout at ${new Date().toISOString()}`);

    // Find the acceptance record
    const acceptance = await ParcelAcceptanceModel.findOne({
      where: {
        parcel_id: parcelId,
        status: 'accepted'
      },
      include: [
        {
          model: ParcelModel,
          as: 'parcel'
        }
      ]
    });

    if (!acceptance) {
      console.log(`⚠️ No active acceptance found for parcel ${parcelId} - may have been picked up or cancelled already`);
      return;
    }

    console.log(`📋 Found acceptance for parcel ${parcelId}, current status: ${acceptance.status}, parcel status: ${acceptance.parcel.status}`);

    // Double check that the parcel is still in accepted status
    if (acceptance.status !== 'accepted' || acceptance.parcel.status !== 'accepted') {
      console.log(`⚠️ Parcel ${parcelId} is no longer in accepted status, skipping timeout`);
      return;
    }

    // Start transaction to ensure data consistency
    const transaction = await sequelize.transaction();

    try {
      // Delete the acceptance record
      await acceptance.destroy({ transaction });
      console.log(`🗑️ Deleted acceptance record for parcel ${parcelId}`);

      // Update parcel status back to pending
      await acceptance.parcel.update({ status: "pending" }, { transaction });
      console.log(`📝 Updated parcel ${parcelId} status to pending`);

      await transaction.commit();
      console.log(`✅ Successfully returned parcel ${parcelId} to marketplace at ${new Date().toISOString()}`);

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Transaction failed for parcel ${parcelId}:`, error);
      throw error;
    }

  } catch (error) {
    console.error(`❌ Error returning parcel ${parcelId} to marketplace:`, error);
  }
};

/**
 * Initialize timeout system - schedule timeouts for existing accepted parcels
 */
const initializeTimeoutSystem = async () => {
  try {
    console.log('🚀 Initializing pickup timeout system...');

    // Find all currently accepted parcels and schedule their timeouts
    const acceptedParcels = await ParcelAcceptanceModel.findAll({
      where: {
        status: 'accepted'
      },
      include: [
        {
          model: ParcelModel,
          as: 'parcel',
          where: {
            status: 'accepted'
          }
        }
      ]
    });

    for (const acceptance of acceptedParcels) {
      if (acceptance.pickup_deadline) {
        schedulePickupTimeout(acceptance.parcel_id, new Date(acceptance.pickup_deadline));
      }
    }

    console.log(`✅ Scheduled timeouts for ${acceptedParcels.length} existing accepted parcels`);

  } catch (error) {
    console.error('❌ Error initializing timeout system:', error);
  }
};

/**
 * Start the timeout system
 */
const startTimeoutSystem = () => {
  console.log('🚀 Starting immediate pickup timeout system...');
  initializeTimeoutSystem();
};

/**
 * Stop the timeout system
 */
const stopTimeoutSystem = () => {
  console.log('🛑 Stopping pickup timeout system...');
  // Clear all active timeouts
  for (const [parcelId, timeoutData] of activeTimeouts) {
    clearTimeout(timeoutData.timeoutId);
    console.log(`Cancelled timeout for parcel ${parcelId} (unique ID: ${timeoutData.uniqueId})`);
  }
  activeTimeouts.clear();
  console.log('All timeouts cleared');
};

/**
 * Get active timeouts for debugging
 */
const getActiveTimeouts = () => {
  const timeouts = [];
  for (const [parcelId, timeoutData] of activeTimeouts) {
    timeouts.push({
      parcelId,
      uniqueId: timeoutData.uniqueId,
      deadline: timeoutData.deadline,
      createdAt: timeoutData.createdAt
    });
  }
  return timeouts;
};

module.exports = {
  schedulePickupTimeout,
  cancelPickupTimeout,
  returnParcelToMarketplace,
  startTimeoutSystem,
  stopTimeoutSystem,
  getActiveTimeouts,
  // Legacy exports for backward compatibility
  startCronJobs: startTimeoutSystem,
  stopCronJobs: stopTimeoutSystem
};
