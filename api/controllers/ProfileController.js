var md5 = require("md5");
const db = require("../models");
const User = db.user;
const Profile = db.profile;
var jwt = require("jsonwebtoken");
const sendmail = require("../utils/Sendmail.js");
const formidable = require("formidable");
const fs = require("fs");
const Constants = require("../config/Constants.js");

exports.getProfile = (req, res) => {
  User.findAll({
    where: {
      id: req.userId,
    },
    include: [
      {
        model: Profile,
        as: "profile",
        rejectOnEmpty: true,
        required: false,
      },
    ],
  })
    .then(async (data) => {
      if (data.length != 0) {
        const userData = data[0];
        delete userData.dataValues.password;
        const token = jwt.sign({ id: data[0].id }, Constants.jwt_token_secret, {});
        const newObject = Object.assign(userData.dataValues);
        if (newObject.profile?.profile_pic_hash) {
          newObject.imageUrl =
            req.protocol +
            "://" +
            req.get("host") +
            "/" +
            userData.dataValues.profile.profile_pic_hash +
            "." +
            userData.dataValues.profile.profile_pic_ext;
        } else {
          newObject.imageUrl = "";
        }
        const dataObject = {
          status: true,
          message: "Profile get successfully!",
          token: token,
          userData: newObject,
        };
        res.send(dataObject);
      } else {
        res.status(400).send({
          status: false,
          message: "Email or Password not Match!",
        });
      }
    })
    .catch((err) => {
      res.status(500).send({
        status: false,
        message: err.message || "Something went wrong.",
      });
    });
};

exports.updateProfile = async (req, res) => {
  try {
    const form = new formidable.IncomingForm({
      hashAlgorithm: "sha1",
    });
    form.parse(req, async function (err, fields, files) {
      Object.keys(fields).forEach((key) => {
        if (Array.isArray(fields[key]) && fields[key].length === 1) {
          fields[key] = fields[key][0]; // Extract string from array
        }
      });

      if (!!files.file && files.file.length > 0) {
        const oldPath = files.file[0].filepath;
        const newPath =
          __dirname + "/../uploads" + "/" + files.file[0].hash + ".png";
        const rawData = fs.readFileSync(oldPath);
        fs.writeFile(newPath, rawData, async function (err) {
          if (err) {
            console.log(err);
          } else {
            const userUpdate = {
              name: fields.name,
            };
            const profileUpdate = {
              profile_pic_hash: files.file[0].hash,
              profile_pic_ext: "png",
              gender: fields.gender,
              phone_number: fields.phone_number
            };
            if (fields.date_of_birth) {
              const parsedDate = new Date(fields.date_of_birth);
              profileUpdate.date_of_birth = parsedDate;
            }
            await User.update(userUpdate, {
              where: { id: req.userId },
            });
            await Profile.update(profileUpdate, {
              where: { userId: req.userId },
            });
            const data = await User.findOne({
              where: { id: req.userId },
              include: [
                {
                  model: Profile,
                  as: "profile",
                  rejectOnEmpty: true,
                  required: false,
                },
              ],
            });
            const userData = data;
            delete userData.dataValues.password;
            const token = jwt.sign({ id: data.id }, Constants.jwt_token_secret, {});
            const newObject = Object.assign(userData.dataValues);
            if (newObject.profile?.profile_pic_hash) {
              newObject.imageUrl =
                req.protocol +
                "://" +
                req.get("host") +
                "/" +
                userData.dataValues.profile.profile_pic_hash +
                "." +
                userData.dataValues.profile.profile_pic_ext;
            } else {
              newObject.imageUrl = "";
            }
            const dataObject = {
              status: true,
              message: "Profile updated Successfully.",
              token: token,
              userData: newObject,
            };
            res.send(dataObject);
          }
        });
      } else {
        const userUpdate = {
          name: fields.name,
        };
        await User.update(userUpdate, {
          where: { id: req.userId },
        });
        const profileUpdate = {
          gender: fields.gender,
          phone_number: fields.phone_number
        };
        if (fields.date_of_birth) {
          const parsedDate = new Date(fields.date_of_birth);
          profileUpdate.date_of_birth = parsedDate;
        }
        await Profile.update(profileUpdate, {
          where: { userId: req.userId },
        });
        const data = await User.findOne({
          where: { id: req.userId },
          include: [
            {
              model: Profile,
              as: "profile",
              rejectOnEmpty: true,
              required: false,
            },
          ],
        });
        const userData = data;
        delete userData.dataValues.password;
        const token = jwt.sign({ id: data.id }, Constants.jwt_token_secret, {});
        const newObject = Object.assign(userData.dataValues);
        if (newObject.profile) {
          newObject.imageUrl =
            req.protocol +
            "://" +
            req.get("host") +
            "/" +
            userData.dataValues.profile.profile_pic_hash +
            "." +
            userData.dataValues.profile.profile_pic_ext;
        } else {
          newObject.imageUrl = "";
        }
        const dataObject = {
          status: true,
          message: "Profile updated Successfully.",
          token: token,
          userData: newObject,
        };
        res.send(dataObject);
      }
    });
  } catch (error) {
    res.status(500).send({
      status: false,
      message: error.message || "Something went wrong.",
    });
  }
};
