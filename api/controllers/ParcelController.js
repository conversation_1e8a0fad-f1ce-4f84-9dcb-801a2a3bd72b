const fs = require("fs");
const path = require("path");
const formidable = require("formidable");
const db = require("../models");
const ParcelModel = db.parcel;
const ParcelMediaModel = db.parcelMedia;
const sequelize = db.sequelize;
const CategoryModel = db.category;
const ParcelAcceptanceModel = db.parcelAcceptance;
const UserModel = db.user;
const ProfileModel = db.profile;
const WalletModel = db.wallet;
const WalletTransactionModel = db.walletTransaction;
const { formatParcelMedia } = require("../helpers/mediaHelper");
const { Op } = require("sequelize");
const otpGenerator = require("otp-generator");
const sendmail = require("../utils/Sendmail.js");
const { schedulePickupTimeout, cancelPickupTimeout } = require("../services/cronService");

// Create Parcel with Media Upload
exports.createParcel = async (req, res) => {
  try {
    const form = new formidable.IncomingForm({ hashAlgorithm: "sha1" });
    form.parse(req, async (err, fields, files) => {
      if (err)
        return res
          .status(400)
          .json({ status: false, message: "Error parsing form." });
      Object.keys(fields).forEach((key) => {
        if (Array.isArray(fields[key]) && fields[key].length === 1) {
          fields[key] = fields[key][0]; // Extract string from array
        }
      });

      // Validate categoryId presence
      if (!fields.categoryId) {
        return res.status(400).json({
          status: false,
          message: "Category ID is required",
        });
      }

      // Check wallet balance
      const wallet = await WalletModel.findOne({
        where: { userId: req.userId },
      });

      if (!wallet) {
        return res.status(400).json({
          status: false,
          message: "Wallet not found",
        });
      }

      const amount = parseFloat(fields.amount);
      if (wallet.balance < amount) {
        return res.status(400).json({
          status: false,
          message: "Insufficient wallet balance",
        });
      }

      // Start transaction
      const transaction = await sequelize.transaction();

      try {
        // Create parcel
        const parcel = await ParcelModel.create(
          {
            userId: req.userId,
            categoryId: fields.categoryId,
            amount: fields.amount,
            insured: fields.insured,
            pickup_lat: fields.pickup_lat,
            pickup_long: fields.pickup_long,
            pickup_address: fields.pickup_address,
            delivery_lat: fields.delivery_lat,
            delivery_long: fields.delivery_long,
            delivery_address: fields.delivery_address,
            description: fields.description,
            full_name: fields.full_name,
            mobile: fields.mobile,
            email: fields.email,
            weight: fields.weight,
            length: fields.length,
            width: fields.width,
            height: fields.height,
            title: fields.title,
            package_type: fields.package_type,
          },
          { transaction }
        );

        // Update wallet balance
        await wallet.update(
          {
            balance: sequelize.literal(`balance - ${amount}`),
          },
          { transaction }
        );

        // Create wallet transaction
        await WalletTransactionModel.create(
          {
            walletId: wallet.id,
            amount: amount,
            type: "debit",
            description: `Debit for parcel: ${parcel.title || `#${parcel.id}`}`,
          },
          { transaction }
        );

        if (files.file && files.file.length > 0) {
          for (const file of files.file) {
            const oldPath = file.filepath;
            const fileExt = file.originalFilename.split(".").pop();
            const newFileName = `${file.hash}.${fileExt}`;
            const newPath = path.join(__dirname, "../uploads", newFileName);
            fs.renameSync(oldPath, newPath);

            await ParcelMediaModel.create(
              {
                parcelId: parcel.id,
                media_hash: file.hash,
                media_ext: fileExt,
              },
              { transaction }
            );
          }
        }

        await transaction.commit();

        // Fetch the parcel with media to include URLs
        const updatedParcel = await ParcelModel.findByPk(parcel.id, {
          include: [
            { model: ParcelMediaModel, as: "media" },
            { model: CategoryModel, as: "category" },
            {
              model: ParcelAcceptanceModel,
              as: "acceptance",
              include: [{ model: UserModel, as: "deliveryUser" }],
            },
          ],
        });

        res.status(201).json({
          status: true,
          message: "Parcel created successfully",
          parcel: formatParcelMedia(req, updatedParcel),
        });
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

// Get All Parcels with Media URLs
exports.getAllParcels = async (req, res) => {
  try {
    const userId = req.userId; // Get user_id from request params
    const parcels = await ParcelModel.findAll({
      where: { userId: userId }, // Filter parcels by userId
      include: [
        { model: ParcelMediaModel, as: "media" },
        { model: CategoryModel, as: "category" },
        {
          model: ParcelAcceptanceModel,
          as: "acceptance",
          include: [{ model: UserModel, as: "deliveryUser" }],
        },
      ],
    });
    const updatedParcels = parcels.map((parcel) =>
      formatParcelMedia(req, parcel)
    );

    res.status(200).json({ status: true, parcels: updatedParcels });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

// Get Parcel by ID with Media URLs
exports.getParcelById = async (req, res) => {
  try {
    const parcel = await ParcelModel.findOne({
      where: { id: req.params.id },
      include: [
        {
          model: UserModel,
          as: "user",
          include: [{ model: ProfileModel, as: "profile" }],
        },
        { model: ParcelMediaModel, as: "media" },
        { model: CategoryModel, as: "category" },
        {
          model: ParcelAcceptanceModel,
          as: "acceptance",
          include: [{ model: UserModel, as: "deliveryUser" }],
        },
      ],
    });

    if (!parcel)
      return res
        .status(404)
        .json({ status: false, message: "Parcel not found" });

    res
      .status(200)
      .json({ status: true, parcel: formatParcelMedia(req, parcel) });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

// Update Parcel
exports.updateParcel = async (req, res) => {
  try {
    const parcel = await ParcelModel.findByPk(req.params.id, {
      include: [{ model: ParcelMediaModel, as: "media" }],
    });

    if (!parcel) {
      return res
        .status(404)
        .json({ status: false, message: "Parcel not found" });
    }

    const form = new formidable.IncomingForm({ hashAlgorithm: "sha1" });
    form.parse(req, async (err, fields, files) => {
      if (err)
        return res
          .status(400)
          .json({ status: false, message: "Error parsing form." });

      Object.keys(fields).forEach((key) => {
        if (Array.isArray(fields[key]) && fields[key].length === 1) {
          fields[key] = fields[key][0];
        }
      });

      // Handle amount update if it's changed
      if (
        fields.amount &&
        parseFloat(fields.amount) !== parseFloat(parcel.amount)
      ) {
        const wallet = await WalletModel.findOne({
          where: { userId: req.userId },
        });

        if (!wallet) {
          return res.status(400).json({
            status: false,
            message: "Wallet not found",
          });
        }

        const oldAmount = parseFloat(parcel.amount);
        const newAmount = parseFloat(fields.amount);

        // Check if user has enough balance after refund
        if (parseFloat(wallet.balance) + oldAmount < newAmount) {
          return res.status(400).json({
            status: false,
            message: "Insufficient wallet balance for the new amount",
          });
        }

        // Start transaction
        const transaction = await sequelize.transaction();

        try {
          // Update wallet balance
          await wallet.update(
            {
              balance: sequelize.literal(
                `balance + ${oldAmount} - ${newAmount}`
              ),
            },
            { transaction }
          );

          // Create refund transaction
          await WalletTransactionModel.create(
            {
              walletId: wallet.id,
              amount: oldAmount,
              type: "credit",
              description: `Refund for parcel: ${parcel.title || `#${parcel.id}`} amount update`,
            },
            { transaction }
          );

          // Create new debit transaction
          await WalletTransactionModel.create(
            {
              walletId: wallet.id,
              amount: newAmount,
              type: "debit",
              description: `New debit for parcel: ${parcel.title || `#${parcel.id}`} amount update`,
            },
            { transaction }
          );

          await transaction.commit();
        } catch (error) {
          await transaction.rollback();
          throw error;
        }
      }

      await parcel.update(fields);

      if (fields.deletedMedia) {
        try {
          fields.deletedMedia = JSON.parse(fields.deletedMedia);
        } catch (e) {
          fields.deletedMedia = []; // In case the string isn't a valid JSON array
        }
      }

      // Handle media deletion
      if (fields.deletedMedia && Array.isArray(fields.deletedMedia)) {
        for (const mediaId of fields.deletedMedia) {
          const media = await ParcelMediaModel.findByPk(mediaId);
          if (media) {
            /* const filePath = path.join(
              __dirname,
              "../uploads",
              `${media.media_hash}.${media.media_ext}`
            );
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
            } */
            await media.destroy();
          }
        }
      }

      // Save new media files
      if (files.file && files.file.length > 0) {
        for (const file of files.file) {
          const oldPath = file.filepath;
          const fileExt = file.originalFilename.split(".").pop();
          const newFileName = `${file.hash}.${fileExt}`;
          const newPath = path.join(__dirname, "../uploads", newFileName);
          fs.renameSync(oldPath, newPath);

          await ParcelMediaModel.create({
            parcelId: parcel.id,
            media_hash: file.hash,
            media_ext: fileExt,
          });
        }
      }

      const updatedParcel = await ParcelModel.findByPk(parcel.id, {
        include: [{ model: ParcelMediaModel, as: "media" }],
      });

      res.status(200).json({
        status: true,
        message: "Parcel updated successfully",
        parcel: formatParcelMedia(req, updatedParcel),
      });
    });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

// Delete Parcel
exports.deleteParcel = async (req, res) => {
  try {
    const deleted = await ParcelModel.destroy({ where: { id: req.params.id } });

    if (!deleted)
      return res
        .status(404)
        .json({ status: false, message: "Parcel not found" });

    res
      .status(200)
      .json({ status: true, message: "Parcel deleted successfully" });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

exports.searchMarketplace = async (req, res) => {
  try {
    const {
      current_lat,
      current_long,
      max_distance, // in km (for pickup location)
      type, // categoryId
      keyword,
      insured,
      parcel_distance_max, // Maximum distance between pickup and delivery in km
    } = req.query;

    // Convert required numeric values
    const pickupLat = parseFloat(current_lat);
    const pickupLong = parseFloat(current_long);
    const maxDistance = parseFloat(max_distance);
    const parcelDistanceMax = parseFloat(parcel_distance_max);

    const whereCondition = {};

    whereCondition.status = "pending";

    // Filter by category (type)
    if (type) {
      whereCondition.categoryId = type;
    }

    // Filter by insured status
    if (insured !== undefined) {
      whereCondition.insured = insured === "true" ? 1 : 0;
    }

    // Filter by keyword (search in full_name, description, pickup_address, delivery_address)
    if (keyword) {
      whereCondition[Op.or] = [
        { full_name: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } },
        { pickup_address: { [Op.like]: `%${keyword}%` } },
        { delivery_address: { [Op.like]: `%${keyword}%` } },
      ];
    }

    const earthRadiusKm = 6371; // Radius of Earth in kilometers

    const distanceConditions = [];

    // Add pickup location distance filter
    if (!isNaN(pickupLat) && !isNaN(pickupLong) && !isNaN(maxDistance)) {
      distanceConditions.push(
        sequelize.literal(
          `(${earthRadiusKm} * acos(
            cos(radians(${pickupLat})) * cos(radians(pickup_lat)) * 
            cos(radians(pickup_long) - radians(${pickupLong})) + 
            sin(radians(${pickupLat})) * sin(radians(pickup_lat))
          )) <= ${maxDistance}`
        )
      );
    }

    // Add parcel distance filter (pickup to delivery)
    if (!isNaN(parcelDistanceMax)) {
      distanceConditions.push(
        sequelize.literal(
          `(${earthRadiusKm} * acos(
            cos(radians(pickup_lat)) * cos(radians(delivery_lat)) * 
            cos(radians(delivery_long) - radians(pickup_long)) + 
            sin(radians(pickup_lat)) * sin(radians(delivery_lat))
          )) <= ${parcelDistanceMax}`
        )
      );
    }

    let finalWhereCondition = { ...whereCondition };

    if (distanceConditions.length > 0) {
      finalWhereCondition[Op.and] = distanceConditions;
    }

    // Fetch parcels with filtering
    const parcels = await ParcelModel.findAll({
      where: finalWhereCondition,
      include: [
        { model: ParcelMediaModel, as: "media" },
        { model: CategoryModel, as: "category" },
        {
          model: ParcelAcceptanceModel,
          as: "acceptance",
          include: [{ model: UserModel, as: "deliveryUser" }],
        },
      ],
    });

    const updatedParcels = parcels.map((parcel) => {
      const pickupDistance = parseFloat(
        (
          earthRadiusKm *
          Math.acos(
            Math.cos((Math.PI / 180) * pickupLat) *
              Math.cos((Math.PI / 180) * parcel.pickup_lat) *
              Math.cos((Math.PI / 180) * (parcel.pickup_long - pickupLong)) +
              Math.sin((Math.PI / 180) * pickupLat) *
                Math.sin((Math.PI / 180) * parcel.pickup_lat)
          )
        ).toFixed(2) // Rounds to 2 decimal places
      );

      const parcelDistance = parseFloat(
        (
          earthRadiusKm *
          Math.acos(
            Math.cos((Math.PI / 180) * parcel.pickup_lat) *
              Math.cos((Math.PI / 180) * parcel.delivery_lat) *
              Math.cos(
                (Math.PI / 180) * (parcel.delivery_long - parcel.pickup_long)
              ) +
              Math.sin((Math.PI / 180) * parcel.pickup_lat) *
                Math.sin((Math.PI / 180) * parcel.delivery_lat)
          )
        ).toFixed(2)
      );

      return {
        ...formatParcelMedia(req, parcel),
        pickup_distance: pickupDistance, // Distance from current location
        parcel_distance: parcelDistance, // Distance between pickup & delivery
      };
    });

    res.status(200).json({ status: true, parcels: updatedParcels });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

exports.acceptParcel = async (req, res) => {
  try {
    const { parcel_id } = req.body;

    const parcel = await ParcelModel.findByPk(parcel_id);
    if (!parcel) {
      return res.status(404).json({ message: "Parcel not found" });
    }

    if (parcel.userId === req.userId) {
      return res
        .status(400)
        .json({ message: "You cannot accept your own parcel." });
    }

    const existingAcceptance = await ParcelAcceptanceModel.findOne({
      where: { parcel_id },
    });

    if (existingAcceptance) {
      return res.status(400).json({ message: "Parcel already accepted" });
    }

    const userExistingAcceptance = await ParcelAcceptanceModel.findOne({
      where: { delivery_user_id: req.userId, status: "accepted" },
    });
    if (userExistingAcceptance) {
      return res.status(400).json({
        message: "You have already accepted another parcel. Complete it first.",
      });
    }

    const otp = otpGenerator.generate(6, {
      lowerCaseAlphabets: false,
      upperCaseAlphabets: false,
      specialChars: false,
    });

    // Set pickup deadline to 15 minutes from now
    const pickupDeadline = new Date();
    pickupDeadline.setMinutes(pickupDeadline.getMinutes() + 15);

    const acceptance = await ParcelAcceptanceModel.create({
      parcel_id,
      delivery_user_id: req.userId,
      status: "accepted",
      otp,
      pickup_deadline: pickupDeadline,
    });
    await parcel.update({ status: "accepted" });

    // Schedule immediate timeout for this parcel
    schedulePickupTimeout(parcel_id, pickupDeadline);

    return res
      .status(200)
      .json({ message: "Parcel accepted successfully", acceptance });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Server error", error: error.message });
  }
};

exports.getAcceptedParcels = async (req, res) => {
  try {
    const userId = req.userId;
    const acceptedParcels = await ParcelAcceptanceModel.findAll({
      where: { delivery_user_id: userId },
      include: [
        {
          model: ParcelModel,
          as: "parcel",
          include: [
            { model: ParcelMediaModel, as: "media" },
            { model: CategoryModel, as: "category" },
          ],
        },
      ],
    });

    const updatedParcels = acceptedParcels.map((acceptance) => {
      if (acceptance.parcel) {
        const acceptanceData = acceptance.toJSON();

        // Calculate if cancellation is still allowed (within 10 minutes and status is "accepted")
        const acceptedAt = new Date(acceptance.accepted_at);
        const currentTime = new Date();
        const timeDifferenceMinutes = (currentTime - acceptedAt) / (1000 * 60);
        const canCancel = acceptance.status === "accepted" && timeDifferenceMinutes <= 10;
        const timeRemainingMinutes = canCancel ? Math.max(0, 10 - timeDifferenceMinutes) : 0;

        // Calculate pickup deadline information
        const pickupDeadline = new Date(acceptance.pickup_deadline);
        const pickupTimeRemainingMinutes = acceptance.status === "accepted" ?
          Math.max(0, (pickupDeadline - currentTime) / (1000 * 60)) : 0;
        const isPickupOverdue = acceptance.status === "accepted" && currentTime > pickupDeadline;

        return {
          ...acceptanceData,
          parcel: formatParcelMedia(req, acceptance.parcel),
          can_cancel: canCancel,
          time_remaining_minutes: Math.round(timeRemainingMinutes * 100) / 100, // Round to 2 decimal places
          pickup_deadline: acceptance.pickup_deadline,
          pickup_time_remaining_minutes: Math.round(pickupTimeRemainingMinutes * 100) / 100,
          is_pickup_overdue: isPickupOverdue,
        };
      }
      return acceptance;
    });

    res.status(200).json({ status: true, parcels: updatedParcels });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

exports.pickedParcel = async (req, res) => {
  try {
    const { parcel_id, otp } = req.body;

    const parcel = await ParcelModel.findByPk(parcel_id);
    if (!parcel) {
      return res
        .status(404)
        .json({ message: "Parcel not found", status: false });
    }
    const acceptance = await ParcelAcceptanceModel.findOne({
      where: { parcel_id },
    });

    if (!acceptance) {
      return res.status(404).json({
        message: "Parcel not found or not accepted yet",
        status: false,
      });
    }

    if (acceptance.otp !== otp) {
      return res
        .status(400)
        .json({ message: "Invalid OTP. Please try again.", status: false });
    }

    // Generate a new OTP for delivery verification
    const newOtp = otpGenerator.generate(6, {
      lowerCaseAlphabets: false,
      upperCaseAlphabets: false,
      specialChars: false,
    });

    try {
      const sendMailData = {
        from: "<EMAIL>",
        to: parcel.email,
        subject: "Parcel Picked Up",
        text:
          "Parcel is picked up by delivery user. it will reach you shortly. OTP to verify pacel is " +
          newOtp,
      };
      sendmail.sendMail(sendMailData);
    } catch (error) {
      console.log(error);
    }

    // Update parcel status to "picked" & refresh OTP
    await acceptance.update({
      status: "picked",
      picked_at: new Date(),
      otp: newOtp,
    });
    await parcel.update({ status: "picked" });

    // Cancel the pickup timeout since parcel is now picked up
    cancelPickupTimeout(parcel_id);

    // ✅ Send new OTP to the receiver via SMS
    //  When twillio account is available we need to uncomment this and create a function to sendSMS
    // sendSMS(parcel.mobile, `Your delivery OTP is: ${newOtp}`);

    return res
      .status(200)
      .json({ message: "Parcel pickup verified successfully.", status: true });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Server error", error: error.message, status: false });
  }
};

exports.deliverParcel = async (req, res) => {
  try {
    const { parcel_id, otp } = req.body;

    // Check if parcel exists
    const parcel = await ParcelModel.findByPk(parcel_id);
    if (!parcel) {
      return res
        .status(404)
        .json({ message: "Parcel not found", status: false });
    }

    // Check if parcel was picked up
    const acceptance = await ParcelAcceptanceModel.findOne({
      where: { parcel_id, status: "picked" },
    });

    if (!acceptance) {
      return res
        .status(400)
        .json({ message: "Parcel not picked up yet.", status: false });
    }

    // Verify OTP before marking as delivered
    console.log("OTP:", otp);
    console.log("Acceptance OTP:", acceptance.otp);

    if (acceptance.otp !== otp) {
      return res
        .status(400)
        .json({ message: "Invalid OTP. Please try again.", status: false });
    }

    // Find delivery user's wallet
    const deliveryUserWallet = await WalletModel.findOne({
      where: { userId: acceptance.delivery_user_id },
    });

    if (!deliveryUserWallet) {
      return res.status(400).json({
        status: false,
        message: "Delivery user wallet not found",
      });
    }

    // Start transaction
    const transaction = await sequelize.transaction();

    try {
      // Update parcel status to "delivered"
      await acceptance.update(
        { status: "delivered", delivered_at: new Date() },
        { transaction }
      );
      await parcel.update({ status: "delivered" }, { transaction });

      // Credit the delivery amount to delivery user's wallet
      const amount = parseFloat(parcel.amount);
      await deliveryUserWallet.update(
        {
          balance: sequelize.literal(`balance + ${amount}`),
        },
        { transaction }
      );

      // Create wallet transaction
      await WalletTransactionModel.create(
        {
          walletId: deliveryUserWallet.id,
          amount: amount,
          type: "credit",
          description: `Credit for parcel: ${parcel.title || `#${parcel.id}`}`,
        },
        { transaction }
      );

      await transaction.commit();

      return res
        .status(200)
        .json({
          message: "Parcel delivered successfully.",
          status: true
        });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Server error", error: error.message, status: false });
  }
};

exports.getRecentPendingParcels = async (req, res) => {
  try {
    // Assuming status "pending" is represented by a string or specific value (e.g., 0 or 'pending')
    const pendingStatus = "pending"; // or 0 if numeric

    const parcels = await ParcelModel.findAll({
      where: {
        status: pendingStatus,
      },
      order: [["createdAt", "DESC"]],
      limit: 5,
      include: [
        { model: ParcelMediaModel, as: "media" },
        { model: CategoryModel, as: "category" },
        {
          model: ParcelAcceptanceModel,
          as: "acceptance",
          include: [{ model: UserModel, as: "deliveryUser" }],
        },
      ],
    });

    const updatedParcels = parcels.map((parcel) => {
      return formatParcelMedia(req, parcel);
    });

    res.status(200).json({ status: true, parcels: updatedParcels });
  } catch (error) {
    res.status(500).json({ status: false, message: error.message });
  }
};

exports.cancelAcceptance = async (req, res) => {
  try {
    const { parcel_id } = req.body;

    // Validate parcel exists
    const parcel = await ParcelModel.findByPk(parcel_id);
    if (!parcel) {
      return res.status(404).json({
        status: false,
        message: "Parcel not found"
      });
    }

    // Find the acceptance record
    const acceptance = await ParcelAcceptanceModel.findOne({
      where: { parcel_id },
    });

    if (!acceptance) {
      return res.status(404).json({
        status: false,
        message: "Parcel acceptance not found",
      });
    }

    // Verify the requesting user is the one who accepted the parcel
    if (acceptance.delivery_user_id !== req.userId) {
      return res.status(403).json({
        status: false,
        message: "You can only cancel your own accepted parcels",
      });
    }

    // Check if parcel is still in "accepted" status (not picked or delivered)
    if (acceptance.status !== "accepted") {
      return res.status(400).json({
        status: false,
        message: `Cannot cancel parcel that is already ${acceptance.status}`,
      });
    }

    // Check 10-minute time limit
    const acceptedAt = new Date(acceptance.accepted_at);
    const currentTime = new Date();
    const timeDifferenceMinutes = (currentTime - acceptedAt) / (1000 * 60);

    if (timeDifferenceMinutes > 10) {
      return res.status(400).json({
        status: false,
        message: "Cannot cancel acceptance after 10 minutes. Time limit exceeded.",
      });
    }

    // Start transaction to ensure data consistency
    const transaction = await sequelize.transaction();

    try {
      // Delete the acceptance record
      await acceptance.destroy({ transaction });

      // Update parcel status back to pending
      await parcel.update({ status: "pending" }, { transaction });

      await transaction.commit();

      // Cancel the pickup timeout since acceptance is cancelled
      cancelPickupTimeout(parcel_id);

      return res.status(200).json({
        status: true,
        message: "Parcel acceptance cancelled successfully. Parcel is now available for other delivery users.",
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Server error",
      error: error.message,
    });
  }
};

exports.extendPickupTime = async (req, res) => {
  try {
    const { parcel_id } = req.body;

    // Validate parcel exists
    const parcel = await ParcelModel.findByPk(parcel_id);
    if (!parcel) {
      return res.status(404).json({
        status: false,
        message: "Parcel not found"
      });
    }

    // Find the acceptance record
    const acceptance = await ParcelAcceptanceModel.findOne({
      where: { parcel_id },
    });

    if (!acceptance) {
      return res.status(404).json({
        status: false,
        message: "Parcel acceptance not found",
      });
    }

    // Verify the requesting user is the one who accepted the parcel
    if (acceptance.delivery_user_id !== req.userId) {
      return res.status(403).json({
        status: false,
        message: "You can only extend pickup time for your own accepted parcels",
      });
    }

    // Check if parcel is still in "accepted" status (not picked or delivered)
    if (acceptance.status !== "accepted") {
      return res.status(400).json({
        status: false,
        message: `Cannot extend pickup time for parcel that is already ${acceptance.status}`,
      });
    }

    // Check if pickup deadline has not passed yet
    const currentTime = new Date();
    const currentDeadline = new Date(acceptance.pickup_deadline);

    if (currentTime > currentDeadline) {
      return res.status(400).json({
        status: false,
        message: "Cannot extend pickup time after the deadline has passed.",
      });
    }

    // Extend pickup deadline by 10 minutes
    const newDeadline = new Date(currentDeadline);
    newDeadline.setMinutes(newDeadline.getMinutes() + 10);

    // Update the pickup deadline
    await acceptance.update({
      pickup_deadline: newDeadline,
    });

    // Reschedule the timeout with the new deadline
    schedulePickupTimeout(parcel_id, newDeadline);

    return res.status(200).json({
      status: true,
      message: "Pickup time extended by 10 minutes successfully.",
      new_pickup_deadline: newDeadline,
    });
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Server error",
      error: error.message,
    });
  }
};

