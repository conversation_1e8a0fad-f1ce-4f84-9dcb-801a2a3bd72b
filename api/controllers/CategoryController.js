const db = require("../models");
const Category = db.category;
const SubCategory = db.subCategory;

// Create a new category
exports.createCategory = async (req, res) => {
  try {
    const { name } = req.body;
    if (!name) {
      return res.status(400).send({ message: "Category name is required!" });
    }

    const category = await Category.create({ name });
    res.status(201).send({ status: true, message: "Category created!", category });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Get all categories
exports.getCategories = async (req, res) => {
  try {
    const categories = await Category.findAll({ include: [{ model: SubCategory, as: "subcategories" }] });
    res.send({ status: true, categories });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Update category
exports.updateCategory = async (req, res) => {
  try {
    const { id, name } = req.body;
    if (!id || !name) {
      return res.status(400).send({ message: "Category ID and name are required!" });
    }

    const category = await Category.update({ name }, { where: { id } });
    res.send({ status: true, message: "Category updated!", category });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Delete category
exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(400).send({ message: "Category ID is required!" });
    }

    await Category.destroy({ where: { id } });
    res.send({ status: true, message: "Category deleted!" });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Create a new subcategory
exports.createSubCategory = async (req, res) => {
  try {
    const { name, categoryId } = req.body;
    if (!name || !categoryId) {
      return res.status(400).send({ message: "SubCategory name and categoryId are required!" });
    }

    const subCategory = await SubCategory.create({ name, categoryId });
    res.status(201).send({ status: true, message: "SubCategory created!", subCategory });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Get all subcategories
exports.getSubCategories = async (req, res) => {
  try {
    const subCategories = await SubCategory.findAll({ include: [{ model: Category, as: "category" }] });
    res.send({ status: true, subCategories });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Update subcategory
exports.updateSubCategory = async (req, res) => {
  try {
    const { id, name, categoryId } = req.body;
    if (!id || !name || !categoryId) {
      return res.status(400).send({ message: "SubCategory ID, name, and categoryId are required!" });
    }

    const subCategory = await SubCategory.update({ name, categoryId }, { where: { id } });
    res.send({ status: true, message: "SubCategory updated!", subCategory });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};

// Delete subcategory
exports.deleteSubCategory = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(400).send({ message: "SubCategory ID is required!" });
    }

    await SubCategory.destroy({ where: { id } });
    res.send({ status: true, message: "SubCategory deleted!" });
  } catch (error) {
    res.status(500).send({ status: false, message: error.message });
  }
};
