const db = require("../models");
const Wallet = db.wallet;
const WalletTransaction = db.walletTransaction;

exports.getWallet = async (req, res) => {
  try {
    const { userId } = req.params;

    const wallet = await Wallet.findOne({
      where: { userId: userId },
      include: [{ model: WalletTransaction, as: "transactions" }],
    });

    if (!wallet) {
      return res.status(404).json({ message: "Wallet not found" });
    }

    res.json(wallet);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

exports.addAmount = async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, description } = req.body;

    let wallet = await Wallet.findOne({ where: { userId: userId } });

    if (!wallet) {
      wallet = await Wallet.create({ userId: userId, balance: 0 });
    }

    wallet.balance = parseFloat(wallet.balance) + parseFloat(amount);
    await wallet.save();

    await WalletTransaction.create({
      walletId: wallet.id,
      amount,
      type: "credit",
      description,
    });

    res.json({ message: "Amount added", balance: wallet.balance });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

exports.getTransactions = async (req, res) => {
  try {
    const { userId } = req.params;

    const wallet = await Wallet.findOne({ where: { userId: userId } });
    if (!wallet) {
      return res.status(404).json({ message: "Wallet not found" });
    }

    const transactions = await WalletTransaction.findAll({
      where: { walletId: wallet.id },
      order: [["createdAt", "DESC"]],
    });

    res.json(transactions);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
