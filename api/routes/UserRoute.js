const { authJwt, verifySignUp } = require("../middleware");

module.exports = app => {
    const users = require("../controllers/UserController.js");

    app.post("/api/user/register", [verifySignUp.checkDuplicateUsernameOrEmail], users.create);

    app.post("/api/user/login", users.login);

    app.post("/api/admin/login", users.adminLogin);

    app.post("/api/user/forgotPassword", users.forgotPassword);

    app.post("/api/user/verifyOtp", users.verifyOtp);

    app.get("/api/user/verify-email", users.verifyEmailLink);

    app.post("/api/user/createNewPassword", [authJwt.verifyToken], users.createNewPassword);

    app.post("/api/user/admin", [authJwt.verifyAdminToken], users.registerAdmin);

    app.get("/api/user/admin", [authJwt.verifyAdminToken], users.getAllAdmin);

    app.get("/api/users", [authJwt.verifyAdminToken], users.getAllUser);

    app.patch("/api/user/update", [authJwt.verifyAdminToken], users.updateUser);

    app.delete("/api/user/delete", [authJwt.verifyAdminToken], users.deleteUser);

};