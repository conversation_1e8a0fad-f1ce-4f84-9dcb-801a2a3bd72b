const { authJwt } = require("../middleware");
const categoryController = require("../controllers/CategoryController.js");

module.exports = (app) => {
  // Category Routes
  app.post("/api/category/add", [authJwt.verifyAdminToken], categoryController.createCategory);
  app.get("/api/categories", [authJwt.verifyAdminToken], categoryController.getCategories);
  app.put("/api/category/update", [authJwt.verifyAdminToken], categoryController.updateCategory);
  app.delete("/api/category/delete/:id", [authJwt.verifyAdminToken], categoryController.deleteCategory);


  app.get("/api/user/categories", [authJwt.verifyToken], categoryController.getCategories);

  // SubCategory Routes
  app.post("/api/subCategory/add", [authJwt.verifyAdminToken], categoryController.createSubCategory);
  app.get("/api/subcategories", [authJwt.verifyAdminToken], categoryController.getSubCategories);
  app.put("/api/subCategory/update", [authJwt.verifyAdminToken], categoryController.updateSubCategory);
  app.delete("/api/subCategory/delete/:id", [authJwt.verifyAdminToken], categoryController.deleteSubCategory);
};
