const { authJwt } = require("../middleware");

module.exports = (app) => {
  const wallet = require("../controllers/WalletController.js");

  // Get wallet details
  app.get("/api/wallet/:userId", [authJwt.verifyToken], wallet.getWallet);

  // Add amount to wallet
  app.post("/api/wallet/:userId/add", [authJwt.verifyToken], wallet.addAmount);

  // Get wallet transactions
  app.get("/api/wallet/:userId/transactions", [authJwt.verifyToken], wallet.getTransactions);
};
