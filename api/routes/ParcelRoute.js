const { authJwt } = require("../middleware");

module.exports = (app) => {
    const parcel = require("../controllers/ParcelController.js");

    app.post("/api/parcels", [authJwt.verifyToken], parcel.createParcel);
    app.get("/api/parcels", [authJwt.verifyToken], parcel.getAllParcels);
    app.get("/api/parcels/:id", [authJwt.verifyToken], parcel.getParcelById);
    app.put("/api/parcels/:id", [authJwt.verifyToken], parcel.updateParcel);
    app.delete("/api/parcels/:id", [authJwt.verifyToken], parcel.deleteParcel);
    app.get("/api/marketplace/search", [authJwt.verifyToken], parcel.searchMarketplace);
    app.get("/api/admin/parcel", [authJwt.verifyAdminToken], parcel.searchMarketplace);

    app.post("/api/parcels/accept", [authJwt.verifyToken], parcel.acceptParcel);
    app.post("/api/parcels/cancel-acceptance", [authJwt.verifyToken], parcel.cancelAcceptance);
    app.get("/api/accepted/parcels", [authJwt.verifyToken], parcel.getAcceptedParcels);

    app.post("/api/parcel/pickup",  [authJwt.verifyToken], parcel.pickedParcel);
    app.post("/api/parcel/deliver",  [authJwt.verifyToken], parcel.deliverParcel);
    app.get("/api/recentParcel",  [authJwt.verifyToken], parcel.getRecentPendingParcels);
};
